<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="incoterm_EXW" model="account.incoterms">
            <field name="code">EXW</field>
            <field name="name">EX WORKS</field>
        </record>
        <record id="incoterm_FCA" model="account.incoterms">
            <field name="code">FCA</field>
            <field name="name">FREE CARRIER</field>
        </record>
        <record id="incoterm_FAS" model="account.incoterms">
            <field name="code">FAS</field>
            <field name="name">FREE ALONGSIDE SHIP</field>
        </record>
        <record id="incoterm_FOB" model="account.incoterms">
            <field name="code">FOB</field>
            <field name="name">FREE ON BOARD</field>
        </record>
        <record id="incoterm_CFR" model="account.incoterms">
            <field name="code">CFR</field>
            <field name="name">COST AND FREIGHT</field>
        </record>
        <record id="incoterm_CIF" model="account.incoterms">
            <field name="code">CIF</field>
            <field name="name">COST, INSURANCE AND FREIGHT</field>
        </record>
        <record id="incoterm_CPT" model="account.incoterms">
            <field name="code">CPT</field>
            <field name="name">CARRIAGE PAID TO</field>
        </record>
        <record id="incoterm_CIP" model="account.incoterms">
            <field name="code">CIP</field>
            <field name="name">CARRIAGE AND INSURANCE PAID TO</field>
        </record>
        <record id="incoterm_DPU" model="account.incoterms">
            <field name="code">DPU</field>
            <field name="name">DELIVERED AT PLACE UNLOADED</field>
        </record>
        <record id="incoterm_DAP" model="account.incoterms">
            <field name="code">DAP</field>
            <field name="name">DELIVERED AT PLACE</field>
        </record>
        <record id="incoterm_DDP" model="account.incoterms">
            <field name="code">DDP</field>
            <field name="name">DELIVERED DUTY PAID</field>
        </record>

    </data>
</odoo>
