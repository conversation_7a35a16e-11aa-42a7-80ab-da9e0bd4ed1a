id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_account_cash_rounding_readonly,account.cash.rounding,model_account_cash_rounding,account.group_account_readonly,1,0,0,0
access_account_cash_rounding_uinvoice,account.cash.rounding,model_account_cash_rounding,account.group_account_invoice,1,1,1,1
access_res_partner_group_account_manager,res_partner group_account_manager,model_res_partner,account.group_account_manager,1,0,0,0

access_account_chart_template,account.chart.template,model_account_chart_template,account.group_account_manager,1,1,1,1
access_account_fiscal_position_template,account.fiscal.position.template,model_account_fiscal_position_template,account.group_account_manager,1,1,1,1
access_account_fiscal_position_tax_template,account.fiscal.position.tax.template,model_account_fiscal_position_tax_template,account.group_account_manager,1,1,1,1
access_account_fiscal_position_account_template,account.fiscal.position.account.template,model_account_fiscal_position_account_template,account.group_account_manager,1,1,1,1
access_product_template_account_manager,product.template.account.manager,product.model_product_template,account.group_account_manager,1,1,1,1
access_account_account_template,account.account.template,model_account_account_template,account.group_account_manager,1,1,1,1
access_account_tax_template,account.tax.template,model_account_tax_template,account.group_account_manager,1,1,1,1
access_account_tax_repartition_line_template_manager,account.tax repartition.line.template.manager,model_account_tax_repartition_line_template,account.group_account_manager,1,1,1,1
access_account_reconcile_model_template,account.reconcile.model.template,model_account_reconcile_model_template,account.group_account_manager,1,1,1,1
access_account_reconcile_model_line_template,account.reconcile.model.line.template,model_account_reconcile_model_line_template,account.group_account_manager,1,1,1,1
access_account_group_template,account.group.template,model_account_group_template,account.group_account_manager,1,1,1,1

access_res_currency_account_manager,res.currency account manager,base.model_res_currency,group_account_manager,1,1,1,1
access_res_currency_rate_account_manager,res.currency.rate account manager,base.model_res_currency_rate,group_account_manager,1,1,1,1

access_account_invoice_report_readonly,account.invoice.report_user,model_account_invoice_report,account.group_account_readonly,1,0,0,0
access_account_invoice_report_billing,account.invoice.report_billing,model_account_invoice_report,account.group_account_invoice,1,0,0,0
access_account_invoice_report,account.invoice.report,model_account_invoice_report,account.group_account_manager,1,1,1,1

access_account_incoterms_all,account.incoterms all,model_account_incoterms,,1,0,0,0
access_account_incoterms_manager,account.incoterms manager,model_account_incoterms,account.group_account_manager,1,1,1,1

access_account_fiscal_position_product_manager,account.fiscal.position account.manager,model_account_fiscal_position,account.group_account_manager,1,1,1,1
access_account_fiscal_position_tax_product_manager,account.fiscal.position.tax account.manager,model_account_fiscal_position_tax,account.group_account_manager,1,1,1,1
access_account_fiscal_position_account_product_manager,account.fiscal.position account.manager,model_account_fiscal_position_account,account.group_account_manager,1,1,1,1
access_account_fiscal_position,account.fiscal.position all,model_account_fiscal_position,base.group_user,1,0,0,0
access_account_fiscal_position_tax,account.fiscal.position.tax all,model_account_fiscal_position_tax,base.group_user,1,0,0,0
access_account_fiscal_position_account,account.fiscal.position all,model_account_fiscal_position_account,base.group_user,1,0,0,0

access_product_product_account_user,product.product.account.user,product.model_product_product,group_account_readonly,1,0,0,0
access_product_product_account_manager,product.product.account.manager,product.model_product_product,account.group_account_manager,1,1,1,1

access_account_bank_statement_group_readonly,account.bank.statement.group.invoice,model_account_bank_statement,account.group_account_readonly,1,0,0,0
access_account_bank_statement_group_invoice,account.bank.statement.group.invoice,model_account_bank_statement,account.group_account_invoice,1,0,0,0
access_account_bank_statement_line_group_readonly,account.bank.statement.line.group.invoice,model_account_bank_statement_line,account.group_account_readonly,1,0,0,0
access_account_bank_statement_line_group_invoice,account.bank.statement.line.group.invoice,model_account_bank_statement_line,account.group_account_invoice,1,0,0,0
access_account_bank_statement,account.bank.statement,model_account_bank_statement,account.group_account_user,1,1,1,1
access_account_bank_statement_line,account.bank.statement.line,model_account_bank_statement_line,account.group_account_user,1,1,1,1

access_account_move_line_manager,account.move.line manager,model_account_move_line,account.group_account_manager,1,0,0,0
access_account_move_manager,account.move manager,model_account_move,account.group_account_manager,1,0,0,0
access_account_move_readonly,account.move readonly,model_account_move,account.group_account_readonly,1,0,0,0
access_account_move_uinvoice,account.move,model_account_move,account.group_account_invoice,1,1,1,1
access_account_move_line_readonly,account.move.line readonly,model_account_move_line,account.group_account_readonly,1,0,0,0
access_account_move_line_uinvoice,account.move.line invoice,model_account_move_line,account.group_account_invoice,1,1,1,1
access_account_invoice_portal,account.move.portal,account.model_account_move,base.group_portal,1,0,0,0
access_account_invoice_line_portal,account.move.line.portal,account.model_account_move_line,base.group_portal,1,0,0,0

access_account_analytic_line_manager,account.analytic.line manager,model_account_analytic_line,account.group_account_manager,1,0,0,0
access_account_analytic_account,account.analytic.account,analytic.model_account_analytic_account,base.group_user,1,0,0,0
access_account_analytic_line_readonly,account.analytic.line invoice,model_account_analytic_line,account.group_account_readonly,1,0,0,0
access_account_analytic_line_invoice,account.analytic.line invoice,model_account_analytic_line,account.group_account_invoice,1,1,1,1
access_account_analytic_accountant,account.analytic.account accountant,analytic.model_account_analytic_account,account.group_account_user,1,1,1,1
access_account_analytic_plan_accountant,account.analytic.plan accountant,analytic.model_account_analytic_plan,account.group_account_user,1,1,1,1
access_account_analytic_distribution_readonly,account.analytic.distribution invoice,analytic.model_account_analytic_distribution_model,account.group_account_readonly,1,0,0,0
access_account_analytic_distribution_invoice,account.analytic.distribution invoice,analytic.model_account_analytic_distribution_model,account.group_account_invoice,1,1,1,1

access_account_journal_readonly,account.journal,model_account_journal,account.group_account_readonly,1,0,0,0
access_account_journal_manager,account.journal,model_account_journal,account.group_account_manager,1,1,1,1
access_account_journal_invoice,account.journal invoice,model_account_journal,account.group_account_invoice,1,0,0,0
access_account_journal_group_all,account.journal.group all,model_account_journal_group,,1,0,0,0
access_account_journal_group_manager,account.journal.group manager,model_account_journal_group,account.group_account_manager,1,1,1,1

access_account_group_manager,account.group,model_account_group,account.group_account_manager,1,1,1,1
access_account_group,account.group,model_account_group,account.group_account_readonly,1,0,0,0
access_account_root_manager,account.root,model_account_root,account.group_account_manager,1,0,0,0
access_account_root,account.root,model_account_root,account.group_account_readonly,1,0,0,0
access_account_account_manager,account.account,model_account_account,account.group_account_manager,1,1,1,1
access_account_account,account.account.readonly,model_account_account,account.group_account_readonly,1,0,0,0
access_account_account_user,account.account user,model_account_account,base.group_user,1,0,0,0
access_account_account_partner_manager,account.account partner manager,model_account_account,base.group_partner_manager,1,0,0,0
access_account_account_invoice,account.account invoice,model_account_account,account.group_account_invoice,1,0,0,0

access_account_tax_internal_user,account.tax internal user,model_account_tax,base.group_user,1,0,0,0
access_account_tax_readonly,account.tax,model_account_tax,account.group_account_readonly,1,0,0,0
access_account_tax_invoice,account.tax,model_account_tax,account.group_account_invoice,1,0,0,0
access_account_tax_manager,account.tax,model_account_tax,account.group_account_manager,1,1,1,1
access_account_account_tax,account.account.tag,model_account_account_tag,account.group_account_user,1,1,1,1
access_account_account_tax_readonly,account.account.tag,model_account_account_tag,account.group_account_readonly,1,0,0,0
access_account_account_tax_user,account.account.tag,model_account_account_tag,account.group_account_invoice,1,0,0,0
access_account_tax_repartition_line_user,account.tax repartition.line.user,model_account_tax_repartition_line,base.group_user,1,0,0,0
access_account_tax_repartition_line_readonly,account.tax repartition.line.invoice,model_account_tax_repartition_line,account.group_account_readonly,1,0,0,0
access_account_tax_repartition_line_invoice,account.tax repartition.line.invoice,model_account_tax_repartition_line,account.group_account_invoice,1,0,0,0
access_account_tax_repartition_line_manager,account.tax repartition.line.manager,model_account_tax_repartition_line,account.group_account_manager,1,1,1,1
access_account_tax_group_internal_user,account.tax.group internal user,model_account_tax_group,base.group_user,1,0,0,0
access_account_tax_group_readonly,account.tax.group,model_account_tax_group,account.group_account_readonly,1,0,0,0
access_account_tax_group,account.tax.group,model_account_tax_group,account.group_account_invoice,1,0,0,0
access_account_tax_group_manager,account.tax.group,model_account_tax_group,account.group_account_manager,1,1,1,1

access_account_reconcile_model_readonly,account.reconcile.model.readonly,model_account_reconcile_model,account.group_account_readonly,1,0,0,0
access_account_reconcile_model_billing,account.reconcile.model.billing,model_account_reconcile_model,account.group_account_invoice,1,0,1,0
access_account_reconcile_model,account.reconcile.model,model_account_reconcile_model,account.group_account_user,1,1,1,1
access_account_reconcile_model_line_readonly,account.reconcile.model.line.readonly,model_account_reconcile_model_line,account.group_account_readonly,1,0,0,0
access_account_reconcile_model_line_billing,account.reconcile.model.line.billing,model_account_reconcile_model_line,account.group_account_invoice,1,0,1,0
access_account_reconcile_model_line,account.reconcile.model.line,model_account_reconcile_model_line,account.group_account_user,1,1,1,1
access_account_reconcile_model_partner_mapping_readonly,account.reconcile.model.partner.mapping.readonly,model_account_reconcile_model_partner_mapping,account.group_account_readonly,1,0,0,0
access_account_reconcile_model_partner_mapping_billing,account.reconcile.model.partner.mapping.billing,model_account_reconcile_model_partner_mapping,account.group_account_invoice,1,0,1,0
access_account_reconcile_model_partner_mapping,account.reconcile.model.partner.mapping,model_account_reconcile_model_partner_mapping,account.group_account_user,1,1,1,1
access_account_partial_reconcile_readonly,account.partial.reconcile.readonly,model_account_partial_reconcile,account.group_account_readonly,1,0,0,0
access_account_partial_reconcile_group_invoice,account.partial.reconcile.group.invoice,model_account_partial_reconcile,account.group_account_invoice,1,1,1,1
access_account_partial_reconcile,account.partial.reconcile,model_account_partial_reconcile,account.group_account_user,1,1,1,1
access_account_full_reconcile_group_readonly,account.full.reconcile.group.invoice,model_account_full_reconcile,account.group_account_readonly,1,0,0,0
access_account_full_reconcile_group_invoice,account.full.reconcile.group.invoice,model_account_full_reconcile,account.group_account_invoice,1,1,1,1
access_account_full_reconcile,account.full.reconcile,model_account_full_reconcile,account.group_account_user,1,1,1,1

access_account_payment_term_partner_manager,account.payment.term partner manager,model_account_payment_term,base.group_user,1,0,0,0
access_account_payment_term_manager,account.payment.term,model_account_payment_term,account.group_account_manager,1,1,1,1
access_account_payment_term_line_partner_manager,account.payment.term.line partner manager,model_account_payment_term_line,base.group_user,1,0,0,0
access_account_payment_term_line_manager,account.payment.term.line,model_account_payment_term_line,account.group_account_manager,1,1,1,1
access_account_payment_method_line_readonly,account.payment.method.line,model_account_payment_method_line,base.group_user,1,0,0,0
access_account_payment_method_line,account.payment.method.line,model_account_payment_method_line,account.group_account_invoice,1,1,1,1
access_account_payment_method_readonly,account.payment.method,model_account_payment_method,base.group_user,1,0,0,0
access_account_payment_method,account.payment.method,model_account_payment_method,account.group_account_invoice,1,1,1,1
access_account_payment_readonly,account.payment,model_account_payment,account.group_account_readonly,1,0,0,0
access_account_payment,account.payment,model_account_payment,account.group_account_invoice,1,1,1,1

access_account_payment_register,access.account.payment.register,model_account_payment_register,account.group_account_invoice,1,1,1,0
access_account_automatic_entry_wizard,access.account.automatic.entry.wizard,model_account_automatic_entry_wizard,account.group_account_user,1,1,1,0
access_account_unreconcile,access.account.unreconcile,model_account_unreconcile,account.group_account_user,1,1,1,0
access_account_resequence,access.account.resequence.wizard,model_account_resequence_wizard,account.group_account_manager,1,1,1,0
access_validate_account_move,access.validate.account.move,model_validate_account_move,account.group_account_invoice,1,1,1,0
access_account_move_reversal,access.account.move.reversal,model_account_move_reversal,account.group_account_invoice,1,1,1,0
access_account_financial_year_op,access.account.financial.year.op,model_account_financial_year_op,account.group_account_manager,1,1,1,0
access_account_setup_bank_manual_config,access.account.setup.bank.manual.config,model_account_setup_bank_manual_config,account.group_account_manager,1,1,1,0
access_account_invoice_send,access.account.invoice.send,model_account_invoice_send,account.group_account_invoice,1,1,1,0

access_account_tour_upload_bill,account.tour.upload.bill,model_account_tour_upload_bill,account.group_account_manager,1,1,1,0
access_account_tour_upload_bill_email_confirm,account.tour.upload.bill.email.confirm,model_account_tour_upload_bill_email_confirm,account.group_account_manager,1,1,1,0

access_account_accrued_orders_wizard,account.account.accrued.orders.wizard,account.model_account_accrued_orders_wizard,group_account_user,1,1,1,0

access_account_report_readonly,account.report.readonly,model_account_report,account.group_account_readonly,1,0,0,0
access_account_report_ac_user,account.report.ac.user,model_account_report,account.group_account_manager,1,1,1,1
access_account_report_line_readonly,account.report.line.readonly,model_account_report_line,account.group_account_readonly,1,0,0,0
access_account_report_line_ac_user,account.report.line.ac.user,model_account_report_line,account.group_account_manager,1,1,1,1
access_account_report_expression_readonly,account.report.expression.readonly,model_account_report_expression,account.group_account_readonly,1,0,0,0
access_account_report_expression_ac_user,account.report.expression.ac.user,model_account_report_expression,account.group_account_manager,1,1,1,1
access_account_report_column_readonly,account.report.column.readonly,model_account_report_column,account.group_account_readonly,1,0,0,0
access_account_report_column_ac_user,account.report.column.ac.user,model_account_report_column,account.group_account_manager,1,1,1,1

access_account_report_external_value_readonly,account.report.external.value.readonly,model_account_report_external_value,account.group_account_readonly,1,0,0,0
access_account_report_external_value_ac_user,account.report.external.value.ac.user,model_account_report_external_value,account.group_account_manager,1,1,1,1
